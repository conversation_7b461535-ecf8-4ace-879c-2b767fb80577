package com.weinuo.quickcommands.ui.screens

import android.app.Activity
import android.content.Intent
import android.graphics.BitmapFactory
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import com.weinuo.quickcommands.ui.components.themed.ThemedRadioButton
import com.weinuo.quickcommands.ui.theme.config.RadioButtonConfig
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.launch
import com.weinuo.quickcommands.data.QuickCommandRepository
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.model.SharedTask
import com.weinuo.quickcommands.model.SharedTriggerCondition
import com.weinuo.quickcommands.ui.activities.IconSelectionActivity
import com.weinuo.quickcommands.ui.components.ConditionCombinationLogic
import com.weinuo.quickcommands.ui.components.SkyBlueRoundedChoiceSelector
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueCardButton
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueNormalButton
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueTextButton
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueSaveButton
import com.weinuo.quickcommands.ui.components.skyblue.IOSStyleTimeRangePicker
import com.weinuo.quickcommands.ui.components.TaskItem
import com.weinuo.quickcommands.ui.components.TriggerConditionItem
import com.weinuo.quickcommands.utils.IconFileManager
import com.weinuo.quickcommands.model.QuickCommand
import com.weinuo.quickcommands.ui.configuration.ConfigurationMode
import com.weinuo.quickcommands.ui.configuration.ConfigurationItem
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.storage.UIStateStorageManager
import com.weinuo.quickcommands.storage.NavigationDataStorageManager

// 旧的 QuickCommandFormScreen 已删除，现在使用 QuickCommandFormContent


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuickCommandFormContent(
    commandId: String?,
    quickCommandRepository: QuickCommandRepository,
    onNavigateBack: () -> Unit,
    onNavigateToUnifiedConfiguration: (ConfigurationMode, String?, Int?) -> Unit,
    onNavigateToDetailedConfiguration: (ConfigurationItem, ConfigurationMode, String?, Int?) -> Unit,
    pendingConfigurationResult: Triple<String, Int?, String>? = null,
    onConfigurationResultProcessed: () -> Unit = {}
) {
    // 判断是否为编辑模式
    val isEditMode = commandId != null

    // 协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 获取当前context
    val context = androidx.compose.ui.platform.LocalContext.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 获取天空蓝主题的UI间距配置
    val uiSpacingConfigManager = remember {
        com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository)
    }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleMedium.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.formSectionTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleMedium
    }

    // 创建存储管理器实例
    val uiStateManager = remember { UIStateStorageManager(context) }
    val navigationDataManager = remember { NavigationDataStorageManager(context) }

    // 获取要编辑的指令（仅在编辑模式下）
    val existingCommand = if (isEditMode) {
        quickCommandRepository.quickCommands.value.find { it.id == commandId!! }
    } else null

    // 如果是编辑模式但找不到指令，返回上一页
    if (isEditMode && existingCommand == null) {
        LaunchedEffect(Unit) {
            onNavigateBack()
        }
        return
    }

    // 使用更稳定的状态管理，避免导航过程中状态丢失
    val stableKey = commandId ?: "new_command"

    // 状态变量 - 根据模式初始化，使用rememberSaveable保存状态
    var commandName by rememberSaveable(key = stableKey + "_command_name") {
        mutableStateOf(existingCommand?.name ?: "")
    }

    var isAllDayEffective by rememberSaveable(key = stableKey + "_is_all_day_effective") {
        mutableStateOf(existingCommand?.isAllDayEffective ?: true)
    }

    var effectiveStartTime by rememberSaveable(key = stableKey + "_effective_start_time") {
        mutableStateOf(
            if (existingCommand?.isAllDayEffective == false)
                existingCommand.effectiveStartTime
            else ""
        )
    }

    var effectiveEndTime by rememberSaveable(key = stableKey + "_effective_end_time") {
        mutableStateOf(
            if (existingCommand?.isAllDayEffective == false)
                existingCommand.effectiveEndTime
            else ""
        )
    }

    // 图标相关状态
    var hasCustomIcon by rememberSaveable(key = stableKey + "_has_custom_icon") {
        mutableStateOf(existingCommand?.hasCustomIcon ?: false)
    }

    var iconPath by rememberSaveable(key = stableKey + "_icon_path") {
        mutableStateOf(existingCommand?.iconUri)
    }

    // 图标选择结果处理
    val iconSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data = result.data
            val newIconPath = data?.getStringExtra(IconSelectionActivity.EXTRA_ICON_PATH)
            if (newIconPath != null) {
                iconPath = newIconPath
                hasCustomIcon = true
            }
        }
    }

    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            // 选择图片后，启动图标裁剪Activity
            val currentCommandId = commandId ?: java.util.UUID.randomUUID().toString()
            val intent = Intent(context, IconSelectionActivity::class.java).apply {
                putExtra(IconSelectionActivity.EXTRA_COMMAND_ID, currentCommandId)
                putExtra(IconSelectionActivity.EXTRA_IMAGE_URI, it.toString())
            }
            iconSelectionLauncher.launch(intent)
        }
    }

    // 触发条件列表
    val triggerConditions = remember(stableKey) {
        mutableStateListOf<SharedTriggerCondition>().apply {
            existingCommand?.triggerConditions?.let { addAll(it) }
        }
    }

    // 任务列表
    val tasks = remember(stableKey) {
        mutableStateListOf<SharedTask>().apply {
            existingCommand?.tasks?.let { addAll(it) }
        }
    }

    // 中止条件列表
    val abortConditions = remember(stableKey) {
        mutableStateListOf<SharedTriggerCondition>().apply {
            existingCommand?.abortConditions?.let { addAll(it) }
        }
    }

    // 任务启用状态映射 - 使用任务ID作为键
    val taskEnabledStates = remember(stableKey) {
        mutableStateOf(mutableMapOf<String, Boolean>().apply {
            // 初始化所有任务为启用状态
            existingCommand?.tasks?.forEach { task ->
                put(task.id, true)
            }
        })
    }

    // 触发条件启用状态映射 - 使用条件ID作为键
    val triggerConditionEnabledStates = remember(stableKey) {
        mutableStateOf(mutableMapOf<String, Boolean>().apply {
            // 初始化所有触发条件为启用状态
            existingCommand?.triggerConditions?.forEach { condition ->
                put(condition.id, true)
            }
        })
    }

    // 中止条件启用状态映射 - 使用条件ID作为键
    val abortConditionEnabledStates = remember(stableKey) {
        mutableStateOf(mutableMapOf<String, Boolean>().apply {
            // 初始化所有中止条件为启用状态
            existingCommand?.abortConditions?.forEach { condition ->
                put(condition.id, true)
            }
        })
    }

    // 处理配置结果
    LaunchedEffect(pendingConfigurationResult) {
        pendingConfigurationResult?.let { (navigationKey, editIndex, configMode) ->
            val success = handleEditedConfigNative(
                navigationKey = navigationKey,
                configMode = configMode,
                triggerConditions = triggerConditions,
                tasks = tasks,
                abortConditions = abortConditions,
                navigationDataManager = navigationDataManager,
                taskEnabledStates = taskEnabledStates,
                triggerConditionEnabledStates = triggerConditionEnabledStates,
                abortConditionEnabledStates = abortConditionEnabledStates
            )
            onConfigurationResultProcessed()
        }
    }

    var requireAllConditions by rememberSaveable(key = stableKey + "_require_all_conditions") {
        mutableStateOf(existingCommand?.requireAllConditions ?: true)
    }

    var requireAllAbortConditions by rememberSaveable(key = stableKey + "_require_all_abort_conditions") {
        mutableStateOf(existingCommand?.requireAllAbortConditions ?: true)
    }

    // 错误状态
    var showNameError by rememberSaveable(key = stableKey + "_show_name_error") {
        mutableStateOf(false)
    }

    var nameErrorMessage by rememberSaveable(key = stableKey + "_name_error_message") {
        mutableStateOf("")
    }

    var showStartTimeError by rememberSaveable(key = stableKey + "_show_start_time_error") {
        mutableStateOf(false)
    }

    var startTimeErrorMessage by rememberSaveable(key = stableKey + "_start_time_error_message") {
        mutableStateOf("")
    }

    var showEndTimeError by rememberSaveable(key = stableKey + "_show_end_time_error") {
        mutableStateOf(false)
    }

    var endTimeErrorMessage by rememberSaveable(key = stableKey + "_end_time_error_message") {
        mutableStateOf("")
    }

    var showTriggerConditionError by rememberSaveable(key = stableKey + "_show_trigger_condition_error") {
        mutableStateOf(false)
    }

    var triggerConditionErrorMessage by rememberSaveable(key = stableKey + "_trigger_condition_error_message") {
        mutableStateOf("")
    }

    // 重置键，用于清除 rememberSaveable 缓存
    var triggerConditionsResetKey by rememberSaveable(key = stableKey + "_trigger_conditions_reset_key") {
        mutableStateOf(0)
    }

    var tasksResetKey by rememberSaveable(key = stableKey + "_tasks_reset_key") {
        mutableStateOf(0)
    }

    var abortConditionsResetKey by rememberSaveable(key = stableKey + "_abort_conditions_reset_key") {
        mutableStateOf(0)
    }

    // 时间格式验证函数
    val validateTimeFormat: (String) -> Boolean = { time ->
        val regex = Regex("^([01]?[0-9]|2[0-3]):([0-5][0-9])$")
        regex.matches(time)
    }

    // TODO: 添加导航结果监听逻辑
    // 这里需要实现类似原来的导航结果处理，但使用不同的机制

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background, // 设置背景颜色
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = if (isEditMode) "编辑快捷指令" else "新建快捷指令",
                        style = if (themeManager.getCurrentThemeId() == "sky_blue") {
                            // 天空蓝主题：使用全局设置的标题字重和字体大小
                            MaterialTheme.typography.titleLarge.copy(
                                fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                                    "normal" -> FontWeight.Normal
                                    "medium" -> FontWeight.Medium
                                    "bold" -> FontWeight.Bold
                                    else -> FontWeight.Medium
                                },
                                fontSize = globalSettings.screenTitleFontSize.sp
                            )
                        } else {
                            // 海洋蓝主题：保持原有样式
                            MaterialTheme.typography.titleLarge
                        }
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        // 其他主题：使用原有的箭头图标
                        IconButton(onClick = onNavigateBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                },
                actions = {
                    // 保存按钮 - 主题感知
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的保存按钮（勾号图标）
                        SkyBlueSaveButton(
                            onClick = {
                            // 验证指令名称
                            if (commandName.isBlank()) {
                                showNameError = true
                                nameErrorMessage = "请输入指令名称"
                                return@SkyBlueSaveButton
                            }

                            // 验证时间设置
                            if (!isAllDayEffective) {
                                val startTimeValid = if (effectiveStartTime.isBlank()) {
                                    showStartTimeError = true
                                    startTimeErrorMessage = "请输入开始时间"
                                    false
                                } else if (!validateTimeFormat(effectiveStartTime)) {
                                    showStartTimeError = true
                                    startTimeErrorMessage = "请输入有效的时间格式 (HH:MM)"
                                    false
                                } else {
                                    showStartTimeError = false
                                    true
                                }

                                val endTimeValid = if (effectiveEndTime.isBlank()) {
                                    showEndTimeError = true
                                    endTimeErrorMessage = "请输入结束时间"
                                    false
                                } else if (!validateTimeFormat(effectiveEndTime)) {
                                    showEndTimeError = true
                                    endTimeErrorMessage = "请输入有效的时间格式 (HH:MM)"
                                    false
                                } else {
                                    showEndTimeError = false
                                    true
                                }

                                val timeLogicValid = if (startTimeValid && endTimeValid && effectiveStartTime == effectiveEndTime) {
                                    showEndTimeError = true
                                    endTimeErrorMessage = "开始时间和结束时间不能相同"
                                    false
                                } else {
                                    true
                                }

                                if (!startTimeValid || !endTimeValid || !timeLogicValid) {
                                    return@SkyBlueSaveButton
                                }
                            }

                            // 验证触发条件
                            if (triggerConditions.isEmpty()) {
                                showTriggerConditionError = true
                                triggerConditionErrorMessage = "请至少添加一个触发条件"
                                return@SkyBlueSaveButton
                            }

                            if (tasks.isEmpty()) {
                                // TODO: 显示错误提示
                                return@SkyBlueSaveButton
                            }

                            // 保存指令
                            coroutineScope.launch {
                                if (isEditMode && existingCommand != null) {
                                    // 编辑模式：更新现有指令
                                    val updatedCommand = existingCommand.copy(
                                        name = commandName,
                                        isAllDayEffective = isAllDayEffective,
                                        effectiveStartTime = if (isAllDayEffective) "00:00" else effectiveStartTime,
                                        effectiveEndTime = if (isAllDayEffective) "23:59" else effectiveEndTime,
                                        triggerConditions = triggerConditions.toList(),
                                        requireAllConditions = requireAllConditions,
                                        tasks = tasks.toList(),
                                        abortConditions = abortConditions.toList(),
                                        requireAllAbortConditions = requireAllAbortConditions,
                                        iconUri = iconPath,
                                        hasCustomIcon = hasCustomIcon
                                    )
                                    quickCommandRepository.saveCommand(updatedCommand)
                                } else {
                                    // 创建模式：创建新指令
                                    val command = QuickCommand(
                                        id = java.util.UUID.randomUUID().toString(),
                                        name = commandName,
                                        isAllDayEffective = isAllDayEffective,
                                        effectiveStartTime = if (isAllDayEffective) "00:00" else effectiveStartTime,
                                        effectiveEndTime = if (isAllDayEffective) "23:59" else effectiveEndTime,
                                        triggerConditions = triggerConditions.toList(),
                                        requireAllConditions = requireAllConditions,
                                        tasks = tasks.toList(),
                                        abortConditions = abortConditions.toList(),
                                        requireAllAbortConditions = requireAllAbortConditions,
                                        isEnabled = true, // 新建指令默认启用
                                        iconUri = iconPath,
                                        hasCustomIcon = hasCustomIcon
                                    )
                                    quickCommandRepository.saveCommand(command)
                                }

                                // 返回上一页
                                onNavigateBack()
                            }
                        },
                        enabled = commandName.isNotBlank() && triggerConditions.isNotEmpty() && tasks.isNotEmpty()
                    )
                    } else {
                        // 其他主题：使用原有的文本按钮
                        TextButton(
                            onClick = {
                                // 验证指令名称
                                if (commandName.isBlank()) {
                                    showNameError = true
                                    nameErrorMessage = "请输入指令名称"
                                    return@TextButton
                                }

                                // 验证时间设置
                                if (!isAllDayEffective) {
                                    val startTimeValid = if (effectiveStartTime.isBlank()) {
                                        showStartTimeError = true
                                        startTimeErrorMessage = "请输入开始时间"
                                        false
                                    } else if (!validateTimeFormat(effectiveStartTime)) {
                                        showStartTimeError = true
                                        startTimeErrorMessage = "请输入有效的时间格式 (HH:MM)"
                                        false
                                    } else {
                                        showStartTimeError = false
                                        true
                                    }

                                    val endTimeValid = if (effectiveEndTime.isBlank()) {
                                        showEndTimeError = true
                                        endTimeErrorMessage = "请输入结束时间"
                                        false
                                    } else if (!validateTimeFormat(effectiveEndTime)) {
                                        showEndTimeError = true
                                        endTimeErrorMessage = "请输入有效的时间格式 (HH:MM)"
                                        false
                                    } else {
                                        showEndTimeError = false
                                        true
                                    }

                                    val timeLogicValid = if (startTimeValid && endTimeValid && effectiveStartTime == effectiveEndTime) {
                                        showEndTimeError = true
                                        endTimeErrorMessage = "开始时间和结束时间不能相同"
                                        false
                                    } else {
                                        true
                                    }

                                    if (!startTimeValid || !endTimeValid || !timeLogicValid) {
                                        return@TextButton
                                    }
                                }

                                // 验证触发条件
                                if (triggerConditions.isEmpty()) {
                                    showTriggerConditionError = true
                                    triggerConditionErrorMessage = "请至少添加一个触发条件"
                                    return@TextButton
                                }

                                if (tasks.isEmpty()) {
                                    // TODO: 显示错误提示
                                    return@TextButton
                                }

                                // 保存指令
                                coroutineScope.launch {
                                    if (isEditMode && existingCommand != null) {
                                        // 编辑模式：更新现有指令
                                        val updatedCommand = existingCommand.copy(
                                            name = commandName,
                                            isAllDayEffective = isAllDayEffective,
                                            effectiveStartTime = if (isAllDayEffective) "00:00" else effectiveStartTime,
                                            effectiveEndTime = if (isAllDayEffective) "23:59" else effectiveEndTime,
                                            triggerConditions = triggerConditions.toList(),
                                            requireAllConditions = requireAllConditions,
                                            tasks = tasks.toList(),
                                            abortConditions = abortConditions.toList(),
                                            requireAllAbortConditions = requireAllAbortConditions,
                                            iconUri = iconPath,
                                            hasCustomIcon = hasCustomIcon
                                        )
                                        quickCommandRepository.saveCommand(updatedCommand)
                                    } else {
                                        // 创建模式：创建新指令
                                        val command = QuickCommand(
                                            id = java.util.UUID.randomUUID().toString(),
                                            name = commandName,
                                            isAllDayEffective = isAllDayEffective,
                                            effectiveStartTime = if (isAllDayEffective) "00:00" else effectiveStartTime,
                                            effectiveEndTime = if (isAllDayEffective) "23:59" else effectiveEndTime,
                                            triggerConditions = triggerConditions.toList(),
                                            requireAllConditions = requireAllConditions,
                                            tasks = tasks.toList(),
                                            abortConditions = abortConditions.toList(),
                                            requireAllAbortConditions = requireAllAbortConditions,
                                            isEnabled = true, // 新建指令默认启用
                                            iconUri = iconPath,
                                            hasCustomIcon = hasCustomIcon
                                        )
                                        quickCommandRepository.saveCommand(command)
                                    }

                                    // 返回上一页
                                    onNavigateBack()
                                }
                            },
                            enabled = commandName.isNotBlank() && triggerConditions.isNotEmpty() && tasks.isNotEmpty()
                        ) {
                            Text("保存")
                        }
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(innerPadding)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            // 名称部分
            Text(
                text = "名称",
                style = themeAwareTitleStyle,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )

            // 主题感知的名称输入框
            if (themeManager.getCurrentThemeId() == "sky_blue") {
                // 天空蓝主题：使用横线样式的输入框
                Column {
                    BasicTextField(
                        value = commandName,
                        onValueChange = {
                            commandName = it
                            if (showNameError) {
                                showNameError = false
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        textStyle = androidx.compose.ui.text.TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Normal,
                            color = MaterialTheme.colorScheme.onSurface,
                            textAlign = TextAlign.Start
                        ),
                        singleLine = true,
                        cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                        decorationBox = { innerTextField ->
                            Box(
                                contentAlignment = Alignment.CenterStart,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                if (commandName.isEmpty()) {
                                    Text(
                                        text = "输入指令名称",
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        fontSize = 16.sp,
                                        textAlign = TextAlign.Start
                                    )
                                }
                                innerTextField()
                            }
                        }
                    )

                    // 底部横线 - 使用与全局设置界面分割线一致的样式
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(1.dp)
                            .background(
                                if (showNameError) MaterialTheme.colorScheme.error
                                else Color(0x33202020) // 使用与全局设置分割线相同的颜色
                            )
                    )

                    // 错误提示
                    if (showNameError) {
                        Text(
                            text = nameErrorMessage,
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            } else {
                // 其他主题：使用原有的OutlinedTextField
                OutlinedTextField(
                    value = commandName,
                    onValueChange = {
                        commandName = it
                        if (showNameError) {
                            showNameError = false
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("输入指令名称") },
                    isError = showNameError,
                    supportingText = if (showNameError) {
                        { Text(nameErrorMessage) }
                    } else null
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 图标选择部分
            Text(
                text = "图标",
                style = themeAwareTitleStyle,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            if (hasCustomIcon && iconPath != null) {
                // 已添加图标：显示图标预览和操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 图标预览 - 增大尺寸以适应更大空间
                    Box(
                        modifier = Modifier
                            .size(80.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.surfaceVariant),
                        contentAlignment = Alignment.Center
                    ) {
                        // 显示自定义图标
                        val bitmap = remember(iconPath) {
                            try {
                                BitmapFactory.decodeFile(iconPath)
                            } catch (e: Exception) {
                                null
                            }
                        }

                        if (bitmap != null) {
                            Image(
                                bitmap = bitmap.asImageBitmap(),
                                contentDescription = "自定义图标",
                                modifier = Modifier
                                    .size(80.dp)
                                    .clip(CircleShape),
                                contentScale = ContentScale.Crop
                            )
                        } else {
                            // 图标加载失败，显示占位符
                            Icon(
                                imageVector = Icons.Filled.Image,
                                contentDescription = "图标加载失败",
                                tint = MaterialTheme.colorScheme.error,
                                modifier = Modifier.size(32.dp)
                            )
                        }
                    }

                    // 图标操作按钮
                    Column(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 更换图标按钮 - 主题感知
                        if (themeManager.getCurrentThemeId() == "sky_blue") {
                            // 天空蓝主题：使用普通按钮样式
                            SkyBlueNormalButton(
                                text = "更换图标",
                                onClick = {
                                    // 直接启动图片选择器
                                    imagePickerLauncher.launch("image/*")
                                },
                                modifier = Modifier.fillMaxWidth()
                            )
                        } else {
                            // 海洋蓝主题：保持原有样式
                            Button(
                                onClick = {
                                    // 直接启动图片选择器
                                    imagePickerLauncher.launch("image/*")
                                },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Row(
                                    horizontalArrangement = Arrangement.Center,
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Icon(
                                        imageVector = Icons.Filled.Image,
                                        contentDescription = "更换图标"
                                    )
                                    Text(
                                        text = "更换图标",
                                        modifier = Modifier.padding(start = 8.dp)
                                    )
                                }
                            }
                        }

                        // 移除图标按钮 - 主题感知
                        if (themeManager.getCurrentThemeId() == "sky_blue") {
                            // 天空蓝主题：使用纯蓝色文字按钮
                            SkyBlueTextButton(
                                text = "移除图标",
                                onClick = {
                                    hasCustomIcon = false
                                    iconPath = null
                                },
                                modifier = Modifier.fillMaxWidth()
                            )
                        } else {
                            // 海洋蓝主题：保持原有样式
                            OutlinedButton(
                                onClick = {
                                    hasCustomIcon = false
                                    iconPath = null
                                },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Text("移除图标")
                            }
                        }
                    }
                }
            } else {
                // 未添加图标：主题感知按钮
                if (themeManager.getCurrentThemeId() == "sky_blue") {
                    // 天空蓝主题：使用卡片型按钮
                    SkyBlueCardButton(
                        text = "添加图标",
                        onClick = {
                            // 直接启动图片选择器
                            imagePickerLauncher.launch("image/*")
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                } else {
                    // 海洋蓝主题：保持原有样式
                    Button(
                        onClick = {
                            // 直接启动图片选择器
                            imagePickerLauncher.launch("image/*")
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Add,
                                contentDescription = "添加图标"
                            )
                            Text(
                                text = "添加图标",
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 生效时间标题 - 放在卡片外面
            Text(
                text = "生效时间",
                style = themeAwareTitleStyle,
                modifier = Modifier.padding(
                    bottom = if (themeManager.getCurrentThemeId() == "sky_blue") {
                        uiSpacingConfig.settingsItemSpacing.dp
                    } else {
                        8.dp
                    }
                )
            )

            // 生效时间选项 - 根据主题决定是否使用卡片包装
            if (themeManager.getCurrentThemeId() == "sky_blue") {
                // 获取动态卡片样式配置
                val context = LocalContext.current
                val cardStyle = com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)

                // 天空蓝主题：使用卡片包装生效时间选项
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceContainerLow // 与SkyBlueCardButton相同的背景颜色
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp), // 无阴影设计
                    shape = RoundedCornerShape(cardStyle.defaultCornerRadius) // 使用动态圆角配置
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        // 全天生效选项
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { isAllDayEffective = true }
                        ) {
                            ThemedRadioButton(
                                config = RadioButtonConfig(
                                    selected = isAllDayEffective,
                                    onClick = { isAllDayEffective = true },
                                    enabled = true
                                )
                            )
                            Spacer(modifier = Modifier.width(uiSpacingConfig.settingsTitleSpacing.dp))
                            Text(
                                text = "全天生效",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.offset(y = (-1).dp)
                            )
                        }

                        Spacer(modifier = Modifier.height(uiSpacingConfig.settingsItemSpacing.dp))

                        // 自定义时间段选项
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { isAllDayEffective = false }
                        ) {
                            ThemedRadioButton(
                                config = RadioButtonConfig(
                                    selected = !isAllDayEffective,
                                    onClick = { isAllDayEffective = false },
                                    enabled = true
                                )
                            )
                            Spacer(modifier = Modifier.width(uiSpacingConfig.settingsTitleSpacing.dp))
                            Text(
                                text = "自定义时间段",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.offset(y = (-1).dp)
                            )
                        }

                        // 自定义时间段的时间选择器 - 直接包含在同一个卡片中
                        if (!isAllDayEffective) {
                            Spacer(modifier = Modifier.height(uiSpacingConfig.settingsItemSpacing.dp))

                            // 解析当前时间字符串为小时和分钟
                            val startTimeParts = effectiveStartTime.split(":")
                            val endTimeParts = effectiveEndTime.split(":")

                            val startHour = startTimeParts.getOrNull(0)?.toIntOrNull() ?: 9
                            val startMinute = startTimeParts.getOrNull(1)?.toIntOrNull() ?: 0
                            val endHour = endTimeParts.getOrNull(0)?.toIntOrNull() ?: 18
                            val endMinute = endTimeParts.getOrNull(1)?.toIntOrNull() ?: 0

                            IOSStyleTimeRangePicker(
                                startHour = startHour,
                                startMinute = startMinute,
                                endHour = endHour,
                                endMinute = endMinute,
                                onStartTimeChange = { hour, minute ->
                                    effectiveStartTime = String.format("%02d:%02d", hour, minute)
                                    if (showStartTimeError) {
                                        showStartTimeError = false
                                    }
                                },
                                onEndTimeChange = { hour, minute ->
                                    effectiveEndTime = String.format("%02d:%02d", hour, minute)
                                    if (showEndTimeError) {
                                        showEndTimeError = false
                                    }
                                },
                                settingsRepository = settingsRepository
                            )
                        }
                    }
                }
            } else {
                // 其他主题：保持原有布局
                // 全天生效选项
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { isAllDayEffective = true }
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = isAllDayEffective,
                            onClick = { isAllDayEffective = true },
                            enabled = true
                        )
                    )
                    Text(
                        text = "全天生效",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.offset(y = (-1).dp)
                    )
                }

                // 自定义时间段选项
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { isAllDayEffective = false }
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = !isAllDayEffective,
                            onClick = { isAllDayEffective = false },
                            enabled = true
                        )
                    )
                    Text(
                        text = "自定义时间段",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.offset(y = (-1).dp)
                    )
                }
            }

            // 其他主题的自定义时间段输入框
            if (!isAllDayEffective && themeManager.getCurrentThemeId() != "sky_blue") {
                Spacer(modifier = Modifier.height(8.dp))

                // 开始时间
                OutlinedTextField(
                    value = effectiveStartTime,
                    onValueChange = {
                        effectiveStartTime = it
                        if (showStartTimeError) {
                            showStartTimeError = false
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("开始时间 (如 09:00)") },
                    isError = showStartTimeError,
                    supportingText = if (showStartTimeError) {
                        { Text(startTimeErrorMessage) }
                    } else null
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 结束时间
                OutlinedTextField(
                    value = effectiveEndTime,
                    onValueChange = {
                        effectiveEndTime = it
                        if (showEndTimeError) {
                            showEndTimeError = false
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("结束时间 (如 18:00)") },
                    isError = showEndTimeError,
                    supportingText = if (showEndTimeError) {
                        { Text(endTimeErrorMessage) }
                    } else null
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 辅助说明文字
                Text(
                    text = "请输入HH:MM格式的时间，例如09:00。支持跨天设置，如23:00 - 06:00 (次日)。",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 触发条件部分
            Text(
                text = if (isEditMode) "触发条件" else "如果 (触发条件部分)",
                style = themeAwareTitleStyle,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // 显示已添加的条件
            if (triggerConditions.isNotEmpty()) {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    triggerConditions.forEachIndexed { index, condition ->
                        TriggerConditionItem(
                            condition = condition,
                            isEnabled = triggerConditionEnabledStates.value[condition.id] ?: true,
                            onDelete = {
                                triggerConditions.removeAt(index)
                                // 同时移除启用状态
                                triggerConditionEnabledStates.value = triggerConditionEnabledStates.value.toMutableMap().apply {
                                    remove(condition.id)
                                }
                                // 重置状态以清除 rememberSaveable 缓存
                                triggerConditionsResetKey++
                            },
                            onEnabledChanged = { isEnabled ->
                                triggerConditionEnabledStates.value = triggerConditionEnabledStates.value.toMutableMap().apply {
                                    put(condition.id, isEnabled)
                                }
                            },
                            onEdit = {
                                // 使用Activity导航到编辑界面
                                onNavigateToUnifiedConfiguration(ConfigurationMode.TRIGGER_CONDITION, null, index)
                            }
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    // 条件逻辑选择器
                    Spacer(modifier = Modifier.height(8.dp))
                    ConditionLogicSelector(
                        requireAllConditions = requireAllConditions,
                        onSelectionChanged = { requireAllConditions = it }
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }
            } else if (isEditMode) {
                // 编辑模式下显示空状态提示
                Text(
                    text = "暂无触发条件，请点击下方按钮添加",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp)
                )
            }

            // 错误提示
            if (showTriggerConditionError) {
                Text(
                    text = triggerConditionErrorMessage,
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
            }

            // 添加条件按钮 - 主题感知
            if (themeManager.getCurrentThemeId() == "sky_blue") {
                // 天空蓝主题：使用卡片型按钮
                SkyBlueCardButton(
                    text = "添加条件",
                    onClick = {
                        onNavigateToUnifiedConfiguration(ConfigurationMode.TRIGGER_CONDITION, null, null)
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                // 海洋蓝主题：保持原有样式
                Button(
                    onClick = {
                        onNavigateToUnifiedConfiguration(ConfigurationMode.TRIGGER_CONDITION, null, null)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Add,
                            contentDescription = "添加条件"
                        )
                        Text(
                            text = "添加条件",
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 执行任务部分
            Text(
                text = if (isEditMode) "执行任务" else "就 (执行任务部分)",
                style = themeAwareTitleStyle,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // 显示已添加的任务列表
            if (tasks.isNotEmpty()) {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    tasks.forEachIndexed { index, task ->
                        TaskItem(
                            task = task,
                            isEnabled = taskEnabledStates.value[task.id] ?: true,
                            onDelete = {
                                tasks.removeAt(index)
                                // 同时移除启用状态
                                taskEnabledStates.value = taskEnabledStates.value.toMutableMap().apply {
                                    remove(task.id)
                                }
                                // 重置状态以清除 rememberSaveable 缓存
                                tasksResetKey++
                            },
                            onEnabledChanged = { isEnabled ->
                                taskEnabledStates.value = taskEnabledStates.value.toMutableMap().apply {
                                    put(task.id, isEnabled)
                                }
                            },

                            onEdit = {
                                // 使用Activity导航到编辑界面
                                onNavigateToUnifiedConfiguration(ConfigurationMode.TASK, null, index)
                            }
                        )

                        if (index < tasks.size - 1) {
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                }
            } else if (isEditMode) {
                // 编辑模式下显示空状态提示
                Text(
                    text = "暂无任务，请点击下方按钮添加",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp)
                )
            }

            // 添加任务按钮 - 主题感知
            if (themeManager.getCurrentThemeId() == "sky_blue") {
                // 天空蓝主题：使用卡片型按钮
                SkyBlueCardButton(
                    text = "添加任务",
                    onClick = {
                        onNavigateToUnifiedConfiguration(ConfigurationMode.TASK, null, null)
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                // 海洋蓝主题：保持原有样式
                Button(
                    onClick = {
                        onNavigateToUnifiedConfiguration(ConfigurationMode.TASK, null, null)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Add,
                            contentDescription = "添加任务"
                        )
                        Text(
                            text = "添加任务",
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 中止执行条件部分
            Text(
                text = "中止执行条件",
                style = themeAwareTitleStyle,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // 显示已添加的中止条件
            if (abortConditions.isNotEmpty()) {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    abortConditions.forEachIndexed { index, condition ->
                        TriggerConditionItem(
                            condition = condition,
                            isEnabled = abortConditionEnabledStates.value[condition.id] ?: true,
                            onDelete = {
                                abortConditions.removeAt(index)
                                // 同时移除启用状态
                                abortConditionEnabledStates.value = abortConditionEnabledStates.value.toMutableMap().apply {
                                    remove(condition.id)
                                }
                                // 重置状态以清除 rememberSaveable 缓存
                                abortConditionsResetKey++
                            },
                            onEnabledChanged = { isEnabled ->
                                abortConditionEnabledStates.value = abortConditionEnabledStates.value.toMutableMap().apply {
                                    put(condition.id, isEnabled)
                                }
                            },
                            onEdit = {
                                // 使用Activity导航到编辑界面
                                onNavigateToUnifiedConfiguration(ConfigurationMode.ABORT_CONDITION, null, index)
                            }
                        )

                        if (index < abortConditions.size - 1) {
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                }
            }

            // 添加中止条件按钮 - 始终显示，无论是否已添加条件，主题感知
            if (themeManager.getCurrentThemeId() == "sky_blue") {
                // 天空蓝主题：使用卡片型按钮
                SkyBlueCardButton(
                    text = "添加中止条件",
                    onClick = {
                        onNavigateToUnifiedConfiguration(ConfigurationMode.ABORT_CONDITION, null, null)
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                // 海洋蓝主题：保持原有样式
                Button(
                    onClick = {
                        onNavigateToUnifiedConfiguration(ConfigurationMode.ABORT_CONDITION, null, null)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Add,
                            contentDescription = "添加中止条件"
                        )
                        Text(
                            text = "添加中止条件",
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }

            // 条件组合逻辑选择器 - 始终显示，无论条件数量
            Spacer(modifier = Modifier.height(16.dp))

            // 条件组合逻辑
            ConditionCombinationLogic(
                requireAllConditions = requireAllAbortConditions,
                onSelectionChanged = { requireAllAbortConditions = it }
            )

            // 中止条件说明文字
            Text(
                text = "中止条件是可选的。如果设置了中止条件，当条件满足时，将停止执行后续任务。",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 8.dp, bottom = 16.dp)
            )
        }
    }
}



/**
 * 条件逻辑选择器
 *
 * 用于选择"当所有条件满足时"或"当任一条件满足时"
 * 根据当前主题自动选择合适的样式：
 * - 天空蓝主题：使用圆角按钮样式（类似"我的"、"探索"按钮）
 * - 其他主题：使用标准的SegmentedButton样式
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConditionLogicSelector(
    requireAllConditions: Boolean,
    onSelectionChanged: (Boolean) -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val currentThemeId = themeManager.getCurrentThemeId()

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = "触发条件逻辑:",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        if (currentThemeId == "sky_blue") {
            // 天空蓝主题：使用圆角按钮样式
            SkyBlueRoundedChoiceSelector(
                option1Text = "当所有条件满足时",
                option2Text = "当任一条件满足时",
                selectedOption = if (requireAllConditions) 0 else 1,
                onSelectionChanged = { selectedIndex ->
                    onSelectionChanged(selectedIndex == 0)
                }
            )
        } else {
            // 其他主题：使用标准的SegmentedButton样式
            SingleChoiceSegmentedButtonRow(
                modifier = Modifier.fillMaxWidth()
            ) {
                SegmentedButton(
                    selected = requireAllConditions,
                    onClick = { onSelectionChanged(true) },
                    shape = SegmentedButtonDefaults.itemShape(index = 0, count = 2),
                    label = { Text("当所有条件满足时") }
                )

                SegmentedButton(
                    selected = !requireAllConditions,
                    onClick = { onSelectionChanged(false) },
                    shape = SegmentedButtonDefaults.itemShape(index = 1, count = 2),
                    label = { Text("当任一条件满足时") }
                )
            }
        }
    }
}



/**
 * 获取条件类型的描述文字
 */
private fun getConditionDescription(conditionType: String): String {
    return when (conditionType) {
        "app_state" -> "监控应用的前台/后台状态变化，支持任何应用或指定应用"
        "battery_state" -> "监控电池状态变化，包括电量阈值、充电状态等"
        "headphone_state" -> "监控耳机连接状态变化"
        "device_event" -> "监控设备级系统事件，包括GPS状态、屏幕状态、剪贴板变化、通知事件等17种设备事件类型"
        "bluetooth_state" -> "监控蓝牙连接状态变化，支持特定设备监控"
        "wifi_state" -> "监控WiFi连接状态变化"
        "connection_state" -> "监控连接状态变化，包括网络、蓝牙、USB、VPN等连接状态"
        "time_based" -> "监控时间相关事件，包括秒表计时、日出日落、定时触发、周期执行等"
        "memory_below_threshold" -> "当系统可用内存低于阈值时触发"
        "communication_state" -> "监控通信状态变化，包括短信收发、通话状态等"
        "sensor_state" -> "监控传感器状态变化，包括光线、方向、震动等"
        "manual_trigger" -> "通过手动方式触发，支持动态快捷方式、静态快捷方式、桌面小组件、悬浮按钮四种触发方式"
        else -> "触发条件"
    }
}

/**
 * 处理配置结果（复杂保存方法）
 *
 * 基于明确的configMode参数来区分不同的配置类型，从NavigationDataStorageManager中加载配置数据，并更新对应的列表
 *
 * @param navigationKey 导航键
 * @param configMode 配置模式（TRIGGER_CONDITION、ABORT_CONDITION、TASK）
 * @param triggerConditions 触发条件列表
 * @param tasks 任务列表
 * @param abortConditions 中止条件列表
 * @param navigationDataManager 导航数据管理器
 * @param taskEnabledStates 任务启用状态映射
 * @param triggerConditionEnabledStates 触发条件启用状态映射
 * @param abortConditionEnabledStates 中止条件启用状态映射
 * @return 处理是否成功
 */
private suspend fun handleEditedConfigNative(
    navigationKey: String,
    configMode: String,
    triggerConditions: MutableList<SharedTriggerCondition>,
    tasks: MutableList<SharedTask>,
    abortConditions: MutableList<SharedTriggerCondition>,
    navigationDataManager: NavigationDataStorageManager,
    taskEnabledStates: MutableState<MutableMap<String, Boolean>>,
    triggerConditionEnabledStates: MutableState<MutableMap<String, Boolean>>,
    abortConditionEnabledStates: MutableState<MutableMap<String, Boolean>>
): Boolean {
    return try {
        when (configMode) {
            "TRIGGER_CONDITION" -> {
                val editData = navigationDataManager.loadConditionEditData(navigationKey)
                if (editData != null) {
                    val condition = editData.condition
                    val editIndex = editData.editIndex

                    if (editIndex != null && editIndex >= 0 && editIndex < triggerConditions.size) {
                        // 编辑模式：替换现有条件
                        triggerConditions[editIndex] = condition
                    } else {
                        // 添加模式：添加新条件
                        triggerConditions.add(condition)
                        // 为新添加的条件设置启用状态
                        triggerConditionEnabledStates.value = triggerConditionEnabledStates.value.toMutableMap().apply {
                            put(condition.id, true)
                        }
                    }
                    true
                } else {
                    android.util.Log.e("QuickCommandForm", "加载触发条件编辑数据失败: $navigationKey")
                    false
                }
            }
            "ABORT_CONDITION" -> {
                val editData = navigationDataManager.loadConditionEditData(navigationKey)
                if (editData != null) {
                    val condition = editData.condition
                    val editIndex = editData.editIndex

                    if (editIndex != null && editIndex >= 0 && editIndex < abortConditions.size) {
                        // 编辑模式：替换现有中止条件
                        abortConditions[editIndex] = condition
                    } else {
                        // 添加模式：添加新中止条件
                        abortConditions.add(condition)
                        // 为新添加的条件设置启用状态
                        abortConditionEnabledStates.value = abortConditionEnabledStates.value.toMutableMap().apply {
                            put(condition.id, true)
                        }
                    }
                    true
                } else {
                    android.util.Log.e("QuickCommandForm", "加载中止条件编辑数据失败: $navigationKey")
                    false
                }
            }
            "TASK" -> {
                val editData = navigationDataManager.loadTaskEditData(navigationKey)
                if (editData != null) {
                    val task = editData.task
                    val editIndex = editData.editIndex

                    if (editIndex != null && editIndex >= 0 && editIndex < tasks.size) {
                        // 编辑模式：替换现有任务
                        tasks[editIndex] = task
                    } else {
                        // 添加模式：添加新任务
                        tasks.add(task)
                        // 为新添加的任务设置启用状态
                        taskEnabledStates.value = taskEnabledStates.value.toMutableMap().apply {
                            put(task.id, true)
                        }
                    }
                    true
                } else {
                    android.util.Log.e("QuickCommandForm", "加载任务编辑数据失败: $navigationKey")
                    false
                }
            }
            else -> {
                android.util.Log.e("QuickCommandForm", "未知的配置模式: $configMode")
                false
            }
        }

        // 清理导航数据
        navigationDataManager.clearNavigationData(navigationKey)
        true
    } catch (e: Exception) {
        false
    }
}
